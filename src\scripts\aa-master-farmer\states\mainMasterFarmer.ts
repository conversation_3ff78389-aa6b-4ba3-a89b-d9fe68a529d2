import { State, createState } from '../../../api/core/script/state'
import { Bank, Withdraw } from '../../../api/game/bank'
import { Inventory } from '../../../api/game/inventory'
import { Walking } from '../../../api/game/walking'
import { WorldHopping } from '../../../api/game/worldHopping'
import { ItemId } from '../../../data/itemId'
import { createGeState } from '../../../api/script-utils/states/geStates'
import { GeAction } from '../../../api/game/geAction'
import { ResupplyMuleState } from '../../../api/script-utils/mule/resupplyMuleStrategy'
import { GiveToMuleState } from '../../../api/script-utils/mule/giveMuleStrategy'
import { Item } from '../../../api/model/item'
import { TradePackage } from '../../../api/model/tradePackage'
import { BotSettings } from '../../../botSettings'
import { MuleReceiver } from '../../muling/muleReceiver'
import { addEquipmentManager } from '../../../api/script-utils/states/equipmentStates'
import { Npcs } from '../../../api/game/npcs'
import { Tile } from '../../../api/model/tile'
import { Time } from '../../../api/utils/time'
import { Skill } from '../../../api/game/skill'
import { Equipment } from '../../../api/game/equipment'
import { log } from '../../../api/utils/utils'
import { PaintTimer } from '../../../api/utils/paintTimer'
import { MenuOpcode } from '../../../api/model/menuOpcode'
import { Dialogue } from '../../../api/game/dialogue'

export class MainMasterFarmer extends State {
    static stunTimer: PaintTimer = new PaintTimer()

    resupply = new ResupplyMuleState(TradePackage.forAccount(MuleReceiver.PACKAGE_RESUPPLY_2m), 308)
    giveToMule = new GiveToMuleState(TradePackage.forAccount(MuleReceiver.PACKAGE_GIVE), BotSettings.tradeWorldP2p, [new Item(995, 700_000)], [new Item(995, ***********)])

    // Master farmer locations
    masterFarmerSpots = [
        {
            name: 'Draynor Village',
            tile: new Tile(3079, 3256, 0),
            npcNames: ['Master Farmer'],
            minLevel: 38
        },
        {
            name: 'Ardougne',
            tile: new Tile(2662, 3374, 0),
            npcNames: ['Master Farmer'],
            minLevel: 38
        }
    ]

    onFirstExecute(): void {
        addEquipmentManager(this, [Equipment.SLOT_BOOTS, Equipment.SLOT_GLOVES, Equipment.SLOT_HELM, Equipment.SLOT_CHEST, Equipment.SLOT_SWORD, Equipment.SLOT_LEGS])
    }

    onBackgroundAction(): void {
        Walking.setRunAuto()
        
        // Heal with salmon if HP is below 3
        if (Skill.HITPOINTS.getCurrentLevel() <= 3) {
            const salmon = Inventory.getById(ItemId.SALMON)
            if (salmon) {
                salmon.click(57, 2)
                Time.sleep(400, 1000)
                return
            }
        }
    }

    onGameMessage(username: string, message: string): void {
        if (message.includes("You're stunned!")) {
            MainMasterFarmer.stunTimer.reset()
        }
    }

    onAction(): void {
        if (!WorldHopping.switchToP2pExcept()) {
            return
        }

        // Check thieving level requirement
        if (Skill.THIEVING.getCurrentLevel() < 38) {
            log('Need level 38 thieving for master farmer')
            return
        }

        // Check if we need to equip rogue's equipment
        if (!this.hasRogueEquipment()) {
            this.setState(this.equipRogueState)
            return
        }

        this.setState(this.bankingState)
    }

    hasRogueEquipment(): boolean {
        return Equipment.isEquipped(ItemId.ROGUE_TOP) &&
               Equipment.isEquipped(ItemId.ROGUE_MASK) &&
               Equipment.isEquipped(ItemId.ROGUE_TROUSERS) &&
               Equipment.isEquipped(ItemId.ROGUE_GLOVES) &&
               Equipment.isEquipped(ItemId.ROGUE_BOOTS)
    }

    equipRogueState = createState('Equipping Rogue Equipment', () => {
        if (!Equipment.withdrawAndEquip(ItemId.ROGUE_TOP, () => this.geState)) return
        if (!Equipment.withdrawAndEquip(ItemId.ROGUE_MASK, () => this.geState)) return
        if (!Equipment.withdrawAndEquip(ItemId.ROGUE_TROUSERS, () => this.geState)) return
        if (!Equipment.withdrawAndEquip(ItemId.ROGUE_GLOVES, () => this.geState)) return
        if (!Equipment.withdrawAndEquip(ItemId.ROGUE_BOOTS, () => this.geState)) return

        this.setState(this.bankingState)
    })

    bankingState = createState('Banking', () => {
        if (!Bank.openNearest()) {
            return
        }

        // Deposit all items except salmon
        Bank.depositAll()

        // Withdraw salmon if we don't have enough
        if (!Withdraw.all(this.geState,
            Withdraw.id(ItemId.SALMON, 7).minimumAmount(7).exactAmount().ensureSpace(),
        )) {
            return
        }

        this.setState(this.thievingState)
    })

    thievingState = createState('Thieving Master Farmer', () => {
        // Handle seed pouches if inventory is full
        if (Inventory.get().getCount(22521, 22531) >= 28) {
            const pouch = Inventory.get().getAny(22521, 22531)
            pouch?.click(57, 2)
            Time.sleep(600, 900)
            return
        }

        // Check if we need to bank (no inventory space or no food)
        if (Inventory.getFreeSlots() <= 1 || !Inventory.contains(ItemId.SALMON)) {
            this.setState(this.bankingState)
            return
        }

        // Get the appropriate spot based on level (prefer Draynor for easier access)
        const spot = this.masterFarmerSpots[0] // Draynor Village

        // Walk to the spot
        if (!Walking.walkTo(spot.tile, 20)) {
            return
        }

        // Find master farmer NPC
        const masterFarmer = Npcs.get((n) =>
            spot.npcNames.includes(n.definition.name)
        )

        if (!masterFarmer) {
            log('No master farmer found, walking to spot...')
            Walking.walkTo(spot.tile, 5)
            return
        }


        if (!masterFarmer.tile.isReachable()) {
            log('Walking to master farmer...')
            Walking.walkTo(masterFarmer.tile, 1)
            return
        }

        // Wait if stunned
        if (MainMasterFarmer.stunTimer.getElapsed() < 2600) {
            log('Stunned, waiting...')
            return
        }

        // Pickpocket the master farmer
        masterFarmer.click(MenuOpcode.NPC_THIRD_OPTION) // "Pickpocket" option
        Time.sleepCycles(1)
    })

    geState = createGeState(
        () => this,
        () => this.resupply,
        [
            GeAction.item(ItemId.SALMON, 100).gePrice(1.1, 1000)
        ], 'Master Farmer GE'
    )
}
